import React, { useState } from 'react';
import ChatInputBox from '../ChatInputBox';
import { useChatStream } from '@/shared/hooks/common';
import { useAuthCommon } from '@/shared/hooks/useAuthCommon';
import { chatConfigService } from '@/shared/services';

/**
 * Demo component để test ChatInputBox với media upload
 * Chỉ sử dụng cho development/testing
 */
const ChatInputBoxDemo: React.FC = () => {
  const [messages] = useState<string[]>([]);
  const [notifications, setNotifications] = useState<Array<{
    id: string;
    type: 'success' | 'error' | 'warning' | 'info';
    message: string;
  }>>([]);

  const { getToken } = useAuthCommon();
  const chatConfig = chatConfigService.getConfig();

  // Initialize chat stream
  const chatStream = useChatStream({
    agentId: chatConfig.agentId,
    apiBaseUrl: chatConfig.apiBaseUrl,
    sseBaseUrl: chatConfig.sseBaseUrl,
    alwaysApproveToolCall: chatConfig.alwaysApproveToolCall,
    getAuthToken: () => getToken() || '',
    debug: true
  });

  const addNotification = (
    type: 'success' | 'error' | 'warning' | 'info',
    message: string,
    duration?: number
  ) => {
    const id = Date.now().toString();
    const notification = { id, type, message };
    
    setNotifications(prev => [...prev, notification]);
    
    // Auto remove after duration
    setTimeout(() => {
      setNotifications(prev => prev.filter(n => n.id !== id));
    }, duration || 5000);
  };

  const handleOpenMenu = () => {
    console.log('[ChatInputBoxDemo] Menu opened');
    addNotification('info', 'Menu opened', 2000);
  };

  const handleInputChange = (text: string) => {
    console.log('[ChatInputBoxDemo] Input changed:', text);
  };

  const handleKeywordDetected = (keyword: string) => {
    console.log('[ChatInputBoxDemo] Keyword detected:', keyword);
    addNotification('info', `Keyword detected: ${keyword}`, 3000);
  };

  // Mock center notification
  const [centerNotification, setCenterNotification] = useState<{
    message: string;
    type?: 'info' | 'success' | 'warning' | 'error';
    duration?: number;
  } | null>(null);

  // Auto clear center notification
  React.useEffect(() => {
    if (centerNotification) {
      const timer = setTimeout(() => {
        setCenterNotification(null);
      }, centerNotification.duration || 3000);
      
      return () => clearTimeout(timer);
    }
  }, [centerNotification]);

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">ChatInputBox Demo</h1>
      
      {/* Chat Stream Info */}
      <div className="mb-6 p-4 bg-gray-100 dark:bg-gray-800 rounded-lg">
        <h3 className="font-medium mb-2">Chat Stream Status</h3>
        <div className="text-sm space-y-1">
          <div><strong>Thread ID:</strong> {chatStream.threadId || 'Not available'}</div>
          <div><strong>Is Loading:</strong> {chatStream.isLoading ? 'Yes' : 'No'}</div>
          <div><strong>Is Streaming:</strong> {chatStream.isStreaming ? 'Yes' : 'No'}</div>
          <div><strong>Is Connected:</strong> {chatStream.isConnected ? 'Yes' : 'No'}</div>
          <div><strong>Tool Call Interrupt:</strong> {chatStream.toolCallInterrupt ? 'Yes' : 'No'}</div>
        </div>
      </div>

      {/* Center Notification */}
      {centerNotification && (
        <div className={`mb-4 p-3 rounded-lg text-center ${
          centerNotification.type === 'error' ? 'bg-red-100 text-red-700' :
          centerNotification.type === 'warning' ? 'bg-yellow-100 text-yellow-700' :
          centerNotification.type === 'success' ? 'bg-green-100 text-green-700' :
          'bg-blue-100 text-blue-700'
        }`}>
          {centerNotification.message}
        </div>
      )}

      {/* Notifications */}
      {notifications.length > 0 && (
        <div className="mb-4 space-y-2">
          {notifications.map(notification => (
            <div
              key={notification.id}
              className={`p-3 rounded-lg ${
                notification.type === 'error' ? 'bg-red-100 text-red-700' :
                notification.type === 'warning' ? 'bg-yellow-100 text-yellow-700' :
                notification.type === 'success' ? 'bg-green-100 text-green-700' :
                'bg-blue-100 text-blue-700'
              }`}
            >
              <strong>{notification.type.toUpperCase()}:</strong> {notification.message}
            </div>
          ))}
        </div>
      )}

      {/* Messages Log */}
      {messages.length > 0 && (
        <div className="mb-6">
          <h3 className="font-medium mb-2">Messages Sent</h3>
          <div className="space-y-2 max-h-40 overflow-y-auto">
            {messages.map((message, index) => (
              <div key={index} className="p-2 bg-gray-50 dark:bg-gray-700 rounded text-sm">
                {message}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* ChatInputBox */}
      <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-4">
        <h3 className="font-medium mb-4">ChatInputBox Component</h3>
        
        <ChatInputBox
          onOpenMenu={handleOpenMenu}
          onInputChange={handleInputChange}
          onKeywordDetected={handleKeywordDetected}
          placeholder="Type a message or paste/upload images..."
          disabled={false}
          chatStream={chatStream}
          addNotification={addNotification}
          setCenterNotification={setCenterNotification}
        />
      </div>

      {/* Instructions */}
      <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
        <h3 className="font-medium mb-2">Test Instructions</h3>
        <div className="text-sm space-y-1">
          <div>• Type text and press Enter to send</div>
          <div>• Paste images from clipboard (Ctrl+V)</div>
          <div>• Click the + button to upload files</div>
          <div>• Click menu button to test menu functionality</div>
          <div>• Toggle auto approve switch</div>
          <div>• Test voice input button</div>
        </div>
      </div>

      {/* API Format Example */}
      <div className="mt-6 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
        <h3 className="font-medium mb-2">Expected API Format</h3>
        <pre className="text-xs overflow-x-auto">
{`{
  "threadId": "deac627b-8be6-4d15-a050-f2074cdbbc55",
  "contentBlocks": [
    {
      "type": "text",
      "content": "Here are the files you requested:"
    },
    {
      "type": "image",
      "fileId": "img1-a1b2-c3d4-d5e6-f7a8b9c0d1e2",
      "name": "Screenshot.png",
      "path": "media/IMAGE/2025/06/user_1/screenshot.png",
      "tags": [
        "screenshot",
        "ui-mockup"
      ]
    },
    {
      "type": "file",
      "fileId": "f1a2b3c4-d5e6-f7a8-b9c0-d1e2f3a4b5c6",
      "name": "Document.pdf",
      "tags": [
        "brief",
        "q3-planning"
      ]
    }
  ]
}`}
        </pre>
      </div>
    </div>
  );
};

export default ChatInputBoxDemo;
