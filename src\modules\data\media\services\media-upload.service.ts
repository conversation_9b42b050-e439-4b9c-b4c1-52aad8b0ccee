import { apiClient } from '@/shared/api/axios';
import {
  MediaUploadUrlDto,
  MediaCreateResponseDto
} from '../types/media-upload.types';

/**
 * Service cho media upload API
 */
export class MediaUploadService {
  private static readonly API_BASE_URL = '/media';

  /**
   * Tạo URL upload cho media files
   * @param mediaFiles Danh sách thông tin media cần tạo URL upload
   * @returns Promise với danh sách media đã được tạo
   */
  static async createMediaUploadUrl(
    mediaFiles: MediaUploadUrlDto[]
  ): Promise<MediaCreateResponseDto[]> {
    try {
      console.log('[MediaUploadService] Creating upload URLs for:', mediaFiles);

      const response = await apiClient.post<MediaCreateResponseDto[]>(
        `${this.API_BASE_URL}/upload-image`,
        mediaFiles
      );

      console.log('[MediaUploadService] Upload URLs created successfully:', response.result);
      return response.result;
    } catch (error) {
      console.error('[MediaUploadService] Failed to create upload URLs:', error);
      throw error;
    }
  }

  /**
   * Upload file lên S3 sử dụng presigned URL
   * @param file File cần upload
   * @param uploadUrl Presigned URL để upload
   * @param onProgress Callback để track progress
   * @returns Promise<void>
   */
  static async uploadFileToS3(
    file: File,
    uploadUrl: string,
    onProgress?: (progress: number) => void
  ): Promise<void> {
    // try {
      console.log('[MediaUploadService] Uploading file to S3:', file.name);

      // Tạo XMLHttpRequest để có thể track progress
      return new Promise<void>((resolve, reject) => {
        const xhr = new XMLHttpRequest();

        // Track upload progress
        xhr.upload.addEventListener('progress', (event) => {
          if (event.lengthComputable) {
            const progress = Math.round((event.loaded / event.total) * 100);
            console.log(`[MediaUploadService] Upload progress: ${progress}%`);
            if (onProgress) {
              onProgress(progress);
            }
          }
        });

        // Handle completion
        // xhr.addEventListener('load', () => {
        //   if (xhr.status >= 200 && xhr.status < 300) {
        //     console.log('[MediaUploadService] File uploaded successfully');
        //     resolve();
        //   } else {
        //     console.error('[MediaUploadService] Upload failed with status:', xhr.status);
        //     reject(new Error(`Upload failed: ${xhr.statusText}`));
        //   }
        // });

        // // Handle errors
        // xhr.addEventListener('error', () => {
        //   console.error('[MediaUploadService] Upload error occurred');
        //   reject(new Error('Upload failed due to network error'));
        // });

        // // Handle abort
        // xhr.addEventListener('abort', () => {
        //   console.log('[MediaUploadService] Upload aborted');
        //   reject(new Error('Upload aborted'));
        // });

        // Start upload
        xhr.open('PUT', uploadUrl);
        xhr.setRequestHeader('Content-Type', file.type);
        xhr.send(file);
      });
    // } catch (error) {
    //   console.error('[MediaUploadService] Failed to upload file:', error);
    //   throw error;
    // }
  }

  /**
   * Complete upload process: tạo upload URL và upload file
   * @param files Danh sách files cần upload
   * @param onProgress Callback để track progress cho từng file
   * @returns Promise với danh sách media đã upload thành công
   */
  static async uploadMediaFiles(
    files: File[],
    onProgress?: (fileIndex: number, progress: number) => void
  ): Promise<MediaCreateResponseDto[]> {
    try {
      console.log('[MediaUploadService] Starting complete upload process for', files.length, 'files');

      // Bước 1: Tạo upload URLs
      const mediaUploadRequests: MediaUploadUrlDto[] = files.map((file) => ({
        mediaType: file.type,
        fileSize: file.size,
        fileName: file.name,
        description: `Uploaded from chat - ${file.name}`,
        tags: []
      }));

      const mediaResponses = await this.createMediaUploadUrl(mediaUploadRequests);

      // Bước 2: Upload từng file
      const uploadPromises = files.map(async (file, fileIndex) => {
        const mediaInfo = mediaResponses[fileIndex];

        if (!mediaInfo) {
          throw new Error(`No media info provided for file: ${file.name}`);
        }

        if (!mediaInfo.uploadUrl) {
          throw new Error(`No upload URL provided for file: ${file.name}`);
        }

        await this.uploadFileToS3(
          file,
          mediaInfo.uploadUrl,
          (progress) => {
            if (onProgress) {
              onProgress(fileIndex, progress);
            }
          }
        );

        return mediaInfo;
      });

      const results = await Promise.all(uploadPromises);
      console.log('[MediaUploadService] All files uploaded successfully');
      return results.filter((result): result is MediaCreateResponseDto => result !== undefined);

    } catch (error) {
      console.error('[MediaUploadService] Failed to upload media files:', error);
      throw error;
    }
  }

  /**
   * Validate file type cho media upload
   * @param file File cần validate
   * @returns boolean
   */
  static validateFileType(file: File): boolean {
    const allowedTypes = [
      // Images
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp',
      'image/svg+xml',
      // Videos
      'video/mp4',
      'video/webm',
      'video/ogg',
      // Audio
      'audio/mp3',
      'audio/wav',
      'audio/ogg',
      'audio/mpeg'
    ];

    return allowedTypes.includes(file.type);
  }

  /**
   * Validate file size cho media upload
   * @param file File cần validate
   * @param maxSizeInMB Kích thước tối đa (MB)
   * @returns boolean
   */
  static validateFileSize(file: File, maxSizeInMB: number = 50): boolean {
    const maxSizeInBytes = maxSizeInMB * 1024 * 1024;
    return file.size <= maxSizeInBytes;
  }

  /**
   * Validate file cho media upload
   * @param file File cần validate
   * @returns { isValid: boolean, error?: string }
   */
  static validateFile(file: File): { isValid: boolean; error?: string } {
    if (!this.validateFileType(file)) {
      return {
        isValid: false,
        error: `File type ${file.type} is not supported. Please use image, video, or audio files.`
      };
    }

    if (!this.validateFileSize(file)) {
      return {
        isValid: false,
        error: `File size ${(file.size / 1024 / 1024).toFixed(1)}MB exceeds the maximum limit of 50MB.`
      };
    }

    return { isValid: true };
  }
}

export default MediaUploadService;
