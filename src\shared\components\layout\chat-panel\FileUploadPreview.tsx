import { Icon } from '@/shared/components/common';
import React from 'react';
import { useTranslation } from 'react-i18next';

export interface UploadedFile {
  id: string;
  name: string;
  type: string;
  url: string;
  thumbnail?: string;
  isLoading?: boolean;
  source?: 'computer' | 'google-drive' | 'knowledge' | 'media'; // Nguồn của file
  error?: boolean; // Có lỗi khi upload không
  serverMediaId?: string; // ID của media trên server sau khi upload thành công
  serverKey?: string; // Storage key từ server (e.g., "1/IMAGE/2025/06/1750093373854-9e9bc1e6-ce2c-4d22-ac60-347290c41780.jpg")
  serverTags?: string[]; // Tags từ server
}

interface FileUploadPreviewProps {
  files: UploadedFile[];
  onRemove: (id: string) => void;
}

const FileUploadPreview: React.FC<FileUploadPreviewProps> = ({ files, onRemove }) => {
  const { t } = useTranslation();
  console.log('FileUploadPreview rendering with', files.length, 'files', files);
  if (!files.length) return null;

  const getFileIcon = (type: string) => {
    if (type.startsWith('image/')) return 'image';
    if (type.includes('pdf')) return 'pdf';
    if (type.includes('word') || type.includes('doc')) return 'doc';
    if (type.includes('excel') || type.includes('sheet') || type.includes('csv')) return 'sheet';
    if (type.includes('powerpoint') || type.includes('presentation')) return 'presentation';
    return 'file';
  };

  const getFileColor = (type: string) => {
    if (type.includes('pdf')) return 'bg-red-500';
    if (type.includes('word') || type.includes('doc')) return 'bg-blue-500';
    if (type.includes('excel') || type.includes('sheet') || type.includes('csv'))
      return 'bg-green-500';
    if (type.includes('powerpoint') || type.includes('presentation')) return 'bg-orange-500';
    return 'bg-gray-500';
  };

  const getFileExtension = (name: string, type: string) => {
    const extension = name.split('.').pop()?.toUpperCase();
    if (extension) return extension;

    if (type.includes('pdf')) return 'PDF';
    if (type.includes('word') || type.includes('doc')) return 'DOC';
    if (type.includes('excel') || type.includes('sheet')) return 'XLS';
    if (type.includes('csv')) return 'CSV';
    if (type.includes('powerpoint') || type.includes('presentation')) return 'PPT';

    return 'FILE';
  };

  // Lấy badge cho nguồn file
  const getSourceBadge = (source?: string) => {
    switch (source) {
      case 'knowledge':
        return { text: 'Tri thức', color: 'bg-blue-500' };
      case 'media':
        return { text: 'Media', color: 'bg-green-500' };
      case 'google-drive':
        return { text: 'GDrive', color: 'bg-yellow-500' };
      default:
        return null;
    }
  };

  return (
    <div className="flex overflow-x-auto pb-2 mb-2 hide-scrollbar max-h-20">
      <div className="flex gap-2 flex-nowrap">
        {files.map(file => (
          <div
            key={file.id}
            className="relative group flex-shrink-0 flex items-center bg-gray-100 dark:bg-gray-800 rounded-md overflow-hidden"
          >
            {file.error && file.type.startsWith('image/') ? (
              // Hiển thị lỗi cho ảnh
              <div className="w-16 h-16 relative flex items-center justify-center bg-red-100 dark:bg-red-900">
                <Icon name="warning" size="md" className="text-red-500" />
                <div className="absolute bottom-0 left-0 right-0 bg-red-500 text-white text-xs text-center py-1">
                  Lỗi
                </div>
                <button
                  onClick={() => onRemove(file.id)}
                  className="absolute top-1 right-1 bg-gray-700 dark:bg-gray-900 bg-opacity-70 rounded-full p-0.5 text-white opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  <Icon name="close" size="xs" />
                </button>
              </div>
            ) : file.isLoading && file.type.startsWith('image/') ? (
              // Hiển thị loading spinner cho ảnh đang tải lên
              <div className="w-16 h-16 relative flex items-center justify-center bg-gray-200 dark:bg-gray-700">
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="w-6 h-6 border-2 border-primary border-t-transparent rounded-full animate-spin"></div>
                </div>
                <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white text-xs text-center py-1">
                  {t('common.loading')}
                </div>
                <button
                  onClick={() => onRemove(file.id)}
                  className="absolute top-1 right-1 bg-gray-700 dark:bg-gray-900 bg-opacity-70 rounded-full p-0.5 text-white opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  <Icon name="close" size="xs" />
                </button>
              </div>
            ) : file.error ? (
              // Hiển thị lỗi cho file khác
              <div className="flex items-center p-2 relative">
                <div className="bg-red-500 rounded-md p-2 mr-2">
                  <Icon name="warning" size="sm" className="text-white" />
                </div>
                <div className="mr-6">
                  <div className="text-sm text-gray-800 dark:text-white truncate max-w-[150px]">
                    {file.name}
                  </div>
                  <div className="text-xs text-red-500 flex items-center gap-1">
                    <span>{getFileExtension(file.name, file.type)}</span>
                    <span>Lỗi upload</span>
                  </div>
                </div>
                <button
                  onClick={() => onRemove(file.id)}
                  className="absolute top-1 right-1 bg-gray-300 dark:bg-gray-700 rounded-full p-0.5 text-gray-700 dark:text-white opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  <Icon name="close" size="xs" />
                </button>
              </div>
            ) : file.isLoading ? (
              // Hiển thị loading spinner cho file khác đang tải lên
              <div className="flex items-center p-2 relative">
                <div className={`${getFileColor(file.type)} rounded-md p-2 mr-2 relative`}>
                  <Icon name={getFileIcon(file.type)} size="sm" className="text-white opacity-50" />
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  </div>
                </div>
                <div className="mr-6">
                  <div className="text-sm text-gray-800 dark:text-white truncate max-w-[150px]">
                    {file.name}
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400 flex items-center gap-1">
                    <span>{getFileExtension(file.name, file.type)}</span>
                    {getSourceBadge(file.source) && (
                      <span className={`${getSourceBadge(file.source)?.color} text-white px-1 py-0.5 rounded text-xs`}>
                        {getSourceBadge(file.source)?.text}
                      </span>
                    )}
                    <span className="text-primary dark:text-primary-light text-xs">
                      {t('common.loading')}
                    </span>
                  </div>
                </div>
                <button
                  onClick={() => onRemove(file.id)}
                  className="absolute top-1 right-1 bg-gray-300 dark:bg-gray-700 rounded-full p-0.5 text-gray-700 dark:text-white opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  <Icon name="close" size="xs" />
                </button>
              </div>
            ) : file.type.startsWith('image/') ? (
              <div className="w-16 h-16 relative">
                <img
                  src={file.thumbnail || file.url}
                  alt={file.name}
                  className="w-full h-full object-cover"
                />
                <button
                  onClick={() => onRemove(file.id)}
                  className="absolute top-1 right-1 bg-gray-700 dark:bg-gray-900 bg-opacity-70 rounded-full p-0.5 text-white opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  <Icon name="close" size="xs" />
                </button>
              </div>
            ) : (
              <div className="flex items-center p-2 relative">
                <div className={`${getFileColor(file.type)} rounded-md p-2 mr-2`}>
                  <Icon name={getFileIcon(file.type)} size="sm" className="text-white" />
                </div>
                <div className="mr-6">
                  <div className="text-sm text-gray-800 dark:text-white truncate max-w-[150px]">
                    {file.name}
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400 flex items-center gap-1">
                    <span>{getFileExtension(file.name, file.type)}</span>
                    {getSourceBadge(file.source) && (
                      <span className={`${getSourceBadge(file.source)?.color} text-white px-1 py-0.5 rounded text-xs`}>
                        {getSourceBadge(file.source)?.text}
                      </span>
                    )}
                  </div>
                </div>
                <button
                  onClick={() => onRemove(file.id)}
                  className="absolute top-1 right-1 bg-gray-300 dark:bg-gray-700 rounded-full p-0.5 text-gray-700 dark:text-white opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  <Icon name="close" size="xs" />
                </button>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default FileUploadPreview;
