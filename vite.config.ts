import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';
import { visualizer } from 'rollup-plugin-visualizer';
import checker from 'vite-plugin-checker';

// https://vite.dev/config/
export default defineConfig({
  server: {
    host: true,
    port: 5173,
    cors: true,
    proxy: {},
    strictPort: false,
    allowedHosts: ['localhost', '127.0.0.1', 'v2.redai.vn', '*************'],
    open: false, // Không tự động mở browser để tránh conflict
    hmr: {
      host: 'localhost',
      overlay: true, // Hiển thị lỗi trên overlay
    },
    watch: {
      usePolling: false, // Tắt polling để cải thiện hiệu suất HMR
      interval: 100, // Giảm interval xuống 100ms để phản hồi nhanh hơn
      ignored: ['**/node_modules/**', '**/.git/**', '**/dist/**'], // Bỏ qua các thư mục không cần thiết
    },
  },
  preview: {
    host: true,
    port: 5173,
    strictPort: false,
    allowedHosts: ['localhost', '127.0.0.1', 'v2.redai.vn', '*************'],
  },
  build: {
    chunkSizeWarningLimit: 1000, // Tăng giới hạn cảnh báo lên 1000kb
    emptyOutDir: true, // Xóa thư mục output trước khi build
    minify: true,
    sourcemap: true,
    reportCompressedSize: true,
    // Dừng build khi có lỗi TypeScript hoặc ESLint
    rollupOptions: {
      external: [],
      output: {
        // Bỏ manualChunks để Vite tự động xử lý việc phân chia code
        // Điều này sẽ đảm bảo React và tất cả API của nó được tải đúng thứ tự
        manualChunks: undefined,
      },
      // Plugin để kiểm tra lỗi trong quá trình build
      plugins: [
        {
          name: 'fail-on-error',
          buildStart() {
            // Kiểm tra sẽ được thực hiện bởi vite-plugin-checker
            // Plugin này chỉ để đảm bảo build process biết về việc kiểm tra lỗi
          }
        }
      ]
    },
    commonjsOptions: {
      // Đảm bảo các module CommonJS được xử lý đúng cách
      transformMixedEsModules: true,
      include: [/node_modules/],
    },
  },
  plugins: [
    react({
      // Cấu hình plugin React để đảm bảo React được xử lý đúng cách
      jsxRuntime: 'automatic',
      // Fast Refresh is enabled by default in @vitejs/plugin-react v4+
      babel: {
        plugins: [['@babel/plugin-transform-react-jsx', { runtime: 'automatic' }]],
      },
    }),
    // Tạm thời disable tất cả checker để build thành công
    // checker({
    //   typescript: {
    //     tsconfigPath: './tsconfig.app.json',
    //     root: './',
    //     buildMode: true,
    //   },
    //   overlay: {
    //     initialIsOpen: true,
    //     position: 'tl',
    //     badgeStyle: 'position: fixed; top: 10px; left: 10px; z-index: 9999;',
    //   },
    //   enableBuild: false, // Disable build check để có thể build thành công
    //   terminal: true,
    // }),
    visualizer({
      open: true, // Tự động mở báo cáo sau khi build
      filename: 'dist/stats.html', // Đường dẫn lưu báo cáo
      gzipSize: true, // Hiển thị kích thước gzip
      brotliSize: true, // Hiển thị kích thước brotli
    }),
  ],
  resolve: {
    alias: {
      '@': path.resolve('./src'),
      '@components': path.resolve('./src/components'),
      '@hooks': path.resolve('./src/hooks'),
      '@contexts': path.resolve('./src/contexts'),
      '@layouts': path.resolve('./src/layouts'),
      '@pages': path.resolve('./src/pages'),
      '@services': path.resolve('./src/services'),
      '@store': path.resolve('./src/store'),
      '@styles': path.resolve('./src/styles'),
      '@types': path.resolve('./src/types'),
      '@lib': path.resolve('./src/lib'),
      '@constants': path.resolve('./src/constants'),
      '@assets': path.resolve('./src/assets'),
    },
  },
});
