import React, { useState, useRef, useEffect, forwardRef, useMemo, useCallback } from 'react';
import { DatePickerProps } from './types';
import { formatDate, parseDate, validateDate } from './utils';
import { useTheme } from '@/shared/contexts/theme';
import { useTranslation } from 'react-i18next';
import Calendar from './Calendar';
import { Icon } from '@/shared/components/common';
import {
  useFloating,
  autoUpdate,
  offset,
  flip,
  shift,
  useClick,
  useDismiss,
  useRole,
  useInteractions,
  FloatingPortal,
  FloatingFocusManager,
  Placement,
} from '@floating-ui/react';

const DatePicker = forwardRef<HTMLInputElement, DatePickerProps>(
  (
    {
      value,
      onChange,
      format = 'dd/MM/yyyy',
      placeholder,
      label,
      disabled = false,
      disabledDates,
      minDate,
      maxDate,
      clearable = true,
      placement = 'bottom',
      size = 'md',
      fullWidth = false,
      error,
      helperText,
      className = '',
      showCalendarIcon = true,
      autoClose = true,
      showToday = true,
      showWeekNumbers = false,
      firstDayOfWeek = 1,
      weekDayNames,
      monthNames,
      showTodayButton = false,
      todayButtonText,
      clearButtonText,
      onOpen,
      onClose,
      inputRef,
      inputProps,
      calendarIcon,
      iconOnly = false,
      hiddenInput = false,
      noBorder = false,
    },
    // Bỏ qua ref từ forwardRef vì chúng ta sử dụng inputRef
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _ref
  ) => {
    // Không sử dụng ref từ forwardRef
    const { t, i18n } = useTranslation();
    useTheme(); // Sử dụng hook theme mới

    // Refs
    const containerRef = useRef<HTMLDivElement>(null);
    const inputRefInternal = useRef<HTMLInputElement>(null);
    const combinedRef = inputRef || inputRefInternal;

    // States
    const [isOpen, setIsOpen] = useState(false);
    const [inputValue, setInputValue] = useState(() => formatDate(value, format, i18n.language));
    const [calendarMonth, setCalendarMonth] = useState(() => validateDate(value) || new Date());

    // Floating UI setup
    const floatingPlacement = useMemo<Placement>(
      () =>
        placement === 'left'
          ? 'left-start'
          : placement === 'right'
            ? 'right-start'
            : placement === 'top'
              ? 'top-start'
              : 'bottom-start',
      [placement]
    );

    const { refs, floatingStyles, context } = useFloating({
      open: isOpen,
      onOpenChange: setIsOpen,
      placement: floatingPlacement,
      middleware: [
        offset(5),
        flip({
          fallbackAxisSideDirection: 'end',
          crossAxis: false,
        }),
        shift(),
      ],
      whileElementsMounted: autoUpdate,
    });

    const click = useClick(context);
    const dismiss = useDismiss(context, {
      outsidePress: true,
      outsidePressEvent: 'mousedown',
      escapeKey: true,
      referencePress: false,
      referencePressEvent: 'mousedown',
      ancestorScroll: true,
    });
    const role = useRole(context);

    const { getReferenceProps, getFloatingProps } = useInteractions([click, dismiss, role]);

    // Cập nhật giá trị input khi value thay đổi
    useEffect(() => {
      setInputValue(formatDate(value, format, i18n.language));
    }, [value, format, i18n.language]);

    // Xử lý khi calendar mở/đóng
    useEffect(() => {
      if (isOpen) {
        onOpen?.();
      } else {
        onClose?.();
      }
    }, [isOpen, onOpen, onClose]);

    // Xử lý khi input thay đổi
    const handleInputChange = useCallback(
      (e: React.ChangeEvent<HTMLInputElement>) => {
        const newValue = e.target.value;
        setInputValue(newValue);

        // Parse giá trị input thành Date
        const parsedDate = parseDate(newValue, format, i18n.language);
        if (parsedDate) {
          onChange?.(parsedDate);
          setCalendarMonth(parsedDate);
        }
      },
      [format, i18n.language, onChange]
    );

    // Xử lý khi focus vào input
    const handleInputFocus = useCallback(() => {
      if (!disabled) {
        setIsOpen(true);
      }
    }, [disabled]);

    // Xử lý khi nhấn phím Escape
    const handleKeyDown = useCallback(
      (e: React.KeyboardEvent) => {
        if (e.key === 'Escape' && isOpen) {
          setIsOpen(false);
        }
      },
      [isOpen]
    );

    // Xử lý khi chọn ngày từ calendar
    const handleSelectDate = useCallback(
      (date: Date) => {
        onChange?.(date);
        setInputValue(formatDate(date, format, i18n.language));

        if (autoClose) {
          setIsOpen(false);
        }
      },
      [onChange, format, i18n.language, autoClose]
    );

    // Xử lý khi click vào icon calendar
    const handleCalendarIconClick = useCallback(() => {
      if (!disabled) {
        setIsOpen(!isOpen);
        if (!isOpen) {
          inputRefInternal.current?.focus();
        }
      }
    }, [disabled, isOpen]);

    // Xử lý khi click vào icon clear
    const handleClearClick = useCallback(
      (e: React.MouseEvent) => {
        e.stopPropagation();
        onChange?.(null);
        setInputValue('');
      },
      [onChange]
    );

    // Xử lý khi tháng thay đổi trong calendar
    const handleMonthChange = useCallback((newMonth: Date) => {
      setCalendarMonth(newMonth);
    }, []);

    // Size classes
    const sizeClasses = {
      sm: 'h-8 text-sm',
      md: 'h-10',
      lg: 'h-12 text-lg',
    }[size];

    // Width class
    const widthClass = fullWidth ? 'w-full' : '';

    // Base classes
    const baseClasses = 'relative';

    // Combine all classes
    const containerClasses = [baseClasses, widthClass, className].join(' ');

    // Placement classes não são mais necessários com Floating UI
    // const placementClasses = {
    //   top: 'bottom-full mb-1',
    //   bottom: 'top-full mt-1',
    //   left: 'right-full mr-1',
    //   right: 'left-full ml-1',
    // }[placement];

    return (
      <div className={containerClasses} ref={containerRef}>
        {/* Label */}
        {label && (
          <label className="block text-sm font-medium mb-1 text-gray-700 dark:text-gray-300">
            {label}
          </label>
        )}

        {/* Input container */}
        {iconOnly ? (
          <div
            className="inline-flex items-center justify-center"
            ref={refs.setReference}
            {...getReferenceProps()}
          >
            <div
              className={`
              p-2 rounded-md
              ${!noBorder ? 'border border-gray-300 dark:border-gray-600' : ''}
              ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800'}
              transition-colors
            `}
              onClick={handleCalendarIconClick}
              title={placeholder || t('datepicker.selectDate', 'Select date')}
            >
              {calendarIcon || <Icon name="calendar" size={size === 'lg' ? 'md' : 'sm'} />}
              {value && !hiddenInput && (
                <span className="ml-2 text-sm font-medium">
                  {formatDate(value, format, i18n.language)}
                </span>
              )}
            </div>

            {/* Clear button for icon mode */}
            {clearable && value && !disabled && (
              <div
                className="ml-1 p-1 rounded-full cursor-pointer text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800"
                onClick={handleClearClick}
                title={clearButtonText || t('datepicker.clear', 'Clear')}
              >
                <Icon name="close" size="xs" />
              </div>
            )}
          </div>
        ) : (
          <div className="relative" ref={refs.setReference} {...getReferenceProps()}>
            <input
              ref={combinedRef}
              type="text"
              value={inputValue}
              onChange={handleInputChange}
              onFocus={handleInputFocus}
              onKeyDown={handleKeyDown}
              placeholder={placeholder}
              disabled={disabled}
              readOnly
              className={`
              w-full px-3 py-2 rounded-md
              ${error ? 'dark:border dark:border-red-500' : 'border-0 dark:border dark:border-border'}
              ${disabled ? 'bg-gray-100 dark:bg-gray-800 cursor-not-allowed' : 'bg-card-muted'}
              ${sizeClasses}
              text-foreground
              focus:outline-none dark:focus:border-primary
            `}
              {...inputProps}
            />

            {/* Calendar icon */}
            {showCalendarIcon && (
              <div
                className={`absolute right-3 top-1/2 transform -translate-y-1/2 ${disabled ? 'opacity-50' : 'cursor-pointer'}`}
                onClick={handleCalendarIconClick}
              >
                {calendarIcon || <Icon name="calendar" size={size === 'lg' ? 'md' : 'sm'} />}
              </div>
            )}

            {/* Clear button */}
            {clearable && inputValue && !disabled && (
              <div
                className="absolute right-8 top-1/2 transform -translate-y-1/2 cursor-pointer text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                onClick={handleClearClick}
                title={clearButtonText || t('datepicker.clear', 'Clear')}
              >
                <Icon name="close" size="xs" />
              </div>
            )}
          </div>
        )}

        {/* Error message */}
        {error && <p className="mt-1 text-sm text-red-500">{error}</p>}

        {/* Helper text */}
        {helperText && !error && (
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">{helperText}</p>
        )}

        {/* Calendar dropdown */}
        {isOpen && (
          <FloatingPortal>
            <FloatingFocusManager context={context} modal={false} order={['reference', 'content']}>
              <div
                ref={refs.setFloating}
                style={floatingStyles}
                className="z-[9200] datepicker-dropdown shadow-lg rounded-lg overflow-hidden"
                {...getFloatingProps()}
              >
                <Calendar
                  selectedDate={value}
                  onSelectDate={handleSelectDate}
                  month={calendarMonth}
                  onMonthChange={handleMonthChange}
                  disabledDates={disabledDates}
                  minDate={minDate}
                  maxDate={maxDate}
                  showToday={showToday}
                  showWeekNumbers={showWeekNumbers}
                  firstDayOfWeek={firstDayOfWeek}
                  weekDayNames={weekDayNames}
                  monthNames={monthNames}
                  showTodayButton={showTodayButton}
                  todayButtonText={todayButtonText}
                />
              </div>
            </FloatingFocusManager>
          </FloatingPortal>
        )}
      </div>
    );
  }
);

DatePicker.displayName = 'DatePicker';

export default DatePicker;

/**
 * DatePickerFormField - Wrapper component để sử dụng với Form và schema validation
 * Tự động xử lý chuyển đổi giữa Date object và string
 */
export interface DatePickerFormFieldProps extends Omit<DatePickerProps, 'value' | 'onChange'> {
  /**
   * Giá trị có thể là string hoặc Date
   */
  value?: string | Date | null;

  /**
   * Callback trả về string (ISO format)
   */
  onChange?: (value: string) => void;

  /**
   * Format để parse/format string
   * @default "yyyy-MM-dd"
   */
  stringFormat?: string;
}

/**
 * DatePickerFormField component để sử dụng với Form validation
 * Tự động chuyển đổi giữa Date object và string
 */
export const DatePickerFormField: React.FC<DatePickerFormFieldProps> = ({
  value,
  onChange,
  // stringFormat is not used in current implementation but kept for future extensibility
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  stringFormat = 'yyyy-MM-dd',
  ...props
}) => {
  // Chuyển đổi string thành Date để hiển thị
  const dateValue = useMemo(() => {
    if (!value) return null;
    if (value instanceof Date) return value;
    if (typeof value === 'string') {
      try {
        return new Date(value);
      } catch {
        return null;
      }
    }
    return null;
  }, [value]);

  // Xử lý khi DatePicker thay đổi
  const handleChange = useCallback((date: Date | null) => {
    if (!onChange) return;

    if (!date) {
      onChange('');
      return;
    }

    // Chuyển đổi Date thành string theo format
    try {
      const isoString = date.toISOString().split('T')[0]; // YYYY-MM-DD format
      onChange(isoString || '');
    } catch {
      onChange('');
    }
  }, [onChange]);

  return (
    <DatePicker
      {...props}
      value={dateValue}
      onChange={handleChange}
    />
  );
};
