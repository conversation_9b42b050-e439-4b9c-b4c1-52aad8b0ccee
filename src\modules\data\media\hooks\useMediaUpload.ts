import { useState, useCallback } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { MediaUploadService } from '../services/media-upload.service';
import { MediaCreateResponseDto, UploadingMediaFile } from '../types/media-upload.types';
import { MEDIA_QUERY_KEYS } from './useMediaQuery';

/**
 * Hook để upload media files với progress tracking
 */
export const useMediaUpload = () => {
  const [uploadingFiles, setUploadingFiles] = useState<UploadingMediaFile[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const queryClient = useQueryClient();

  /**
   * Upload multiple files
   */
  const uploadFiles = useCallback(
    async (files: File[]): Promise<MediaCreateResponseDto[]> => {
      try {
        setIsUploading(true);

        // Validate files trước khi upload
        const validationResults = files.map(file => ({
          file,
          validation: MediaUploadService.validateFile(file)
        }));

        const invalidFiles = validationResults.filter(result => !result.validation.isValid);
        if (invalidFiles.length > 0) {
          const errors = invalidFiles.map(result => 
            `${result.file.name}: ${result.validation.error}`
          ).join('\n');
          throw new Error(`Invalid files:\n${errors}`);
        }

        // Initialize uploading files state
        const initialUploadingFiles: UploadingMediaFile[] = files.map((file, index) => ({
          file,
          mediaInfo: {
            id: `temp-${Date.now()}-${index}`,
            name: file.name,
            tags: [],
            key: '',
            uploadUrl: ''
          },
          progress: 0,
          status: 'pending'
        }));

        setUploadingFiles(initialUploadingFiles);

        // Upload files với progress tracking
        const results = await MediaUploadService.uploadMediaFiles(
          files,
          (fileIndex, progress) => {
            setUploadingFiles(prev => 
              prev.map((item, index) => 
                index === fileIndex 
                  ? { ...item, progress, status: 'uploading' as const }
                  : item
              )
            );
          }
        );

        // Update final state
        setUploadingFiles(prev =>
          prev.map((item, index) => {
            const result = results[index];
            if (!result) {
              return {
                ...item,
                progress: 100,
                status: 'error' as const,
                error: 'Upload failed: No result returned'
              };
            }
            return {
              ...item,
              mediaInfo: result,
              progress: 100,
              status: 'completed' as const
            };
          })
        );

        // Invalidate media queries để refresh data
        queryClient.invalidateQueries({ queryKey: MEDIA_QUERY_KEYS.lists() });

        console.log('[useMediaUpload] Files uploaded successfully:', results);
        return results;

      } catch (error) {
        console.error('[useMediaUpload] Upload failed:', error);
        
        // Update error state
        setUploadingFiles(prev => 
          prev.map(item => ({
            ...item,
            status: 'error' as const,
            error: error instanceof Error ? error.message : 'Upload failed'
          }))
        );

        throw error;
      } finally {
        setIsUploading(false);
      }
    },
    [queryClient]
  );

  /**
   * Upload single file
   */
  const uploadFile = useCallback(
    async (file: File): Promise<MediaCreateResponseDto> => {
      const results = await uploadFiles([file]);
      const result = results[0];
      if (!result) {
        throw new Error('Upload failed: No result returned');
      }
      return result;
    },
    [uploadFiles]
  );

  /**
   * Clear uploading files state
   */
  const clearUploadingFiles = useCallback(() => {
    setUploadingFiles([]);
  }, []);

  /**
   * Remove specific uploading file
   */
  const removeUploadingFile = useCallback((fileId: string) => {
    setUploadingFiles(prev => prev.filter(item => item.mediaInfo.id !== fileId));
  }, []);

  /**
   * Get upload progress for all files
   */
  const getOverallProgress = useCallback(() => {
    if (uploadingFiles.length === 0) return 0;
    
    const totalProgress = uploadingFiles.reduce((sum, file) => sum + file.progress, 0);
    return Math.round(totalProgress / uploadingFiles.length);
  }, [uploadingFiles]);

  /**
   * Check if any files are currently uploading
   */
  const hasUploadingFiles = uploadingFiles.some(file => 
    file.status === 'uploading' || file.status === 'pending'
  );

  /**
   * Get completed uploads
   */
  const completedUploads = uploadingFiles.filter(file => file.status === 'completed');

  /**
   * Get failed uploads
   */
  const failedUploads = uploadingFiles.filter(file => file.status === 'error');

  return {
    // Upload functions
    uploadFile,
    uploadFiles,
    
    // State
    uploadingFiles,
    isUploading: isUploading || hasUploadingFiles,
    
    // Progress
    getOverallProgress,
    
    // Completed/Failed
    completedUploads,
    failedUploads,
    
    // Actions
    clearUploadingFiles,
    removeUploadingFile,
    
    // Validation
    validateFile: MediaUploadService.validateFile,
    validateFileType: MediaUploadService.validateFileType,
    validateFileSize: MediaUploadService.validateFileSize
  };
};

export default useMediaUpload;
