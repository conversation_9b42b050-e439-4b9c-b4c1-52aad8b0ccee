import { useCallback } from 'react';
import { apiClient } from '@/shared/api/axios';

/**
 * Hook đơn giản để upload files cho chat
 * Trả về format đúng cho chat content blocks
 */
export const useChatMediaUpload = () => {
  
  /**
   * Validate file type
   */
  const validateFileType = useCallback((file: File): boolean => {
    const allowedTypes = [
      'image/jpeg',
      'image/jpg', 
      'image/png',
      'image/gif',
      'image/webp',
      'image/svg+xml',
      'application/pdf',
      'text/plain',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ];
    
    return allowedTypes.includes(file.type);
  }, []);

  /**
   * Validate file với size và type
   */
  const validateFile = useCallback((file: File) => {
    // Check file type
    if (!validateFileType(file)) {
      return {
        isValid: false,
        error: 'Loại file không được hỗ trợ'
      };
    }

    // Check file size (10MB max)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      return {
        isValid: false,
        error: 'File quá lớn (tối đa 10MB)'
      };
    }

    return {
      isValid: true,
      error: null
    };
  }, [validateFileType]);

  /**
   * Upload single file
   */
  const uploadFile = useCallback(async (file: File) => {
    const validation = validateFile(file);
    if (!validation.isValid) {
      throw new Error(validation.error || 'File không hợp lệ');
    }

    // Tạo media data
    const mediaData = {
      name: file.name,
      description: undefined,
      size: file.size,
      tags: ['chat', 'upload'],
      type: file.type,
      viewUrl: URL.createObjectURL(file)
    };

    // Gọi API để tạo presigned URL
    const response = await apiClient.post('/media/presigned-urls', [mediaData]);
    const presignedUrls = response?.result || [];
    
    if (!presignedUrls || presignedUrls.length === 0) {
      throw new Error('Không nhận được URL tải lên từ server');
    }

    const urlInfo = presignedUrls[0];
    
    // Upload file lên S3
    const uploadResponse = await fetch(urlInfo.uploadUrl, {
      method: 'PUT',
      body: file,
      headers: {
        'Content-Type': file.type,
      },
    });

    if (!uploadResponse.ok) {
      throw new Error('Không thể tải file lên server');
    }

    // Trả về thông tin file đã upload
    return {
      id: urlInfo.id,
      name: urlInfo.name,
      key: urlInfo.key,
      tags: urlInfo.tags || ['chat', 'upload']
    };
  }, [validateFile]);

  /**
   * Upload multiple files
   */
  const uploadFiles = useCallback(async (files: File[]) => {
    // Validate tất cả files trước
    for (const file of files) {
      const validation = validateFile(file);
      if (!validation.isValid) {
        throw new Error(`${file.name}: ${validation.error}`);
      }
    }

    // Tạo media data cho tất cả files
    const mediaDataList = files.map(file => ({
      name: file.name,
      description: undefined,
      size: file.size,
      tags: ['chat', 'upload'],
      type: file.type,
      viewUrl: URL.createObjectURL(file)
    }));

    // Gọi API để tạo presigned URLs
    const response = await apiClient.post('/media/presigned-urls', mediaDataList);
    const presignedUrls = response?.result || [];
    
    if (!presignedUrls || presignedUrls.length !== files.length) {
      throw new Error('Không nhận được đủ URL tải lên từ server');
    }

    // Upload tất cả files song song
    const uploadPromises = files.map(async (file, index) => {
      const urlInfo = presignedUrls[index];
      
      const uploadResponse = await fetch(urlInfo.uploadUrl, {
        method: 'PUT',
        body: file,
        headers: {
          'Content-Type': file.type,
        },
      });

      if (!uploadResponse.ok) {
        throw new Error(`Không thể tải file ${file.name} lên server`);
      }

      return {
        id: urlInfo.id,
        name: urlInfo.name,
        key: urlInfo.key,
        tags: urlInfo.tags || ['chat', 'upload']
      };
    });

    return Promise.all(uploadPromises);
  }, [validateFile]);

  return {
    validateFile,
    validateFileType,
    uploadFile,
    uploadFiles,
    isUploading: false
  };
};

export default useChatMediaUpload;
